<template>
  <div class="layer-group">
    <div 
      class="group-header"
      :style="{ paddingLeft: `${group.level * 16 + 12}px` }"
    >
      <button
        class="collapse-button"
        @click="handleCollapseClick"
        :aria-label="isCollapsed ? 'Expand group' : 'Collapse group'"
        :aria-expanded="!isCollapsed"
      >
        <i
          class="fas"
          :class="isCollapsed ? 'fa-chevron-right' : 'fa-chevron-down'"
        ></i>
      </button>
      
      <label class="group-label">
        <input
          type="checkbox"
          :checked="isGroupVisible"
          @change="$emit('toggle-group-visibility', group.id)"
          class="group-checkbox"
        />
        <span class="group-name">{{ group.name }}</span>
      </label>
    </div>
    
    <div
      v-if="!isCollapsed"
      class="group-children"
    >
      <!-- Render child groups recursively -->
      <LayerGroup
        v-for="childGroup in childGroups"
        :key="childGroup.id"
        :group="childGroup"
        @toggle-group-collapse="$emit('toggle-group-collapse', $event)"
        @toggle-group-visibility="$emit('toggle-group-visibility', $event)"
        @toggle-layer-visibility="$emit('toggle-layer-visibility', $event)"
        @update-layer-opacity="$emit('update-layer-opacity', $event)"
      />
      
      <!-- Render child layers -->
      <LayerItem
        v-for="childLayer in childLayers"
        :key="childLayer.id"
        :layer="childLayer"
        @toggle-visibility="$emit('toggle-layer-visibility', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, type Ref } from 'vue';
import type { GroupLayer, GeoLayer } from '../types';
import LayerItem from './LayerItem.vue';

// Props
const props = defineProps<{
  group: GroupLayer;
}>();

// Inject update counter to force reactivity
const updateCounter = inject<Ref<number>>('updateCounter');

// Emits
const emit = defineEmits<{
  'toggle-group-collapse': [groupId: string];
  'toggle-group-visibility': [groupId: string];
  'toggle-layer-visibility': [layerId: string, visible?: boolean];
}>();

// Event handlers
const handleCollapseClick = () => {
  console.log('LayerGroup: Collapse button clicked for group:', props.group.id);
  console.log('LayerGroup: Current collapsed state:', props.group.collapsed);
  emit('toggle-group-collapse', props.group.id);
};

// Computed properties to separate child groups and layers
// Include updateCounter to force reactivity
const childGroups = computed(() => {
  // Access updateCounter to trigger reactivity
  updateCounter?.value;
  return props.group.children.filter(child => 'children' in child) as GroupLayer[];
});

const childLayers = computed(() => {
  // Access updateCounter to trigger reactivity
  updateCounter?.value;
  return props.group.children.filter(child => !('children' in child)) as GeoLayer[];
});

// Reactive collapsed state
const isCollapsed = computed(() => {
  // Access updateCounter to trigger reactivity
  updateCounter?.value;
  return props.group.collapsed;
});

// Reactive visibility state
const isGroupVisible = computed(() => {
  // Access updateCounter to trigger reactivity
  updateCounter?.value;
  return props.group.visible;
});
</script>

<style scoped>
.layer-group {
  border-left: 1px solid #e9ecef;
  margin-left: 4px;
}

.group-header {
  display: flex;
  align-items: center;
  padding: 6px 8px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.group-header:hover {
  background-color: #e9ecef;
}

.collapse-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px;
  margin-right: 6px;
  border-radius: 2px;
  color: #6c757d;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
}

.collapse-button:hover {
  color: #495057;
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(1.1);
}

.collapse-button:focus {
  outline: 2px solid #007bff;
  outline-offset: 1px;
}

.group-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  flex: 1;
  font-weight: 500;
  color: #495057;
}

.group-checkbox {
  margin-right: 6px;
  cursor: pointer;
}

.group-name {
  font-size: 13px;
  user-select: none;
  line-height: 1.3;
}

.group-children {
  background-color: #ffffff;
}

/* Accessibility improvements */
.group-checkbox:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Responsive design */
@media (max-width: 480px) {
  .group-header {
    padding: 4px 6px;
  }

  .group-name {
    font-size: 12px;
  }

  .collapse-button {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }

  .group-checkbox {
    margin-right: 4px;
  }
}
</style>
