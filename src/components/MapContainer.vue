<template>
  <div id="map-container">
    <div id="map" ref="mapElement"></div>
    <FloatingMenu 
      @toggle-layers="toggleLayersPanel"
      @toggle-legend="toggleLegendPanel"
      @toggle-search="toggleSearchPanel"
      @zoom-home="zoomToHome"
    />
    <SearchPanel 
      v-model:visible="searchPanelVisible"
      @close="searchPanelVisible = false"
    />
    <LayersPanel 
      v-model:visible="layersPanelVisible"
      @close="layersPanelVisible = false"
    />
    <LegendPanel 
      v-model:visible="legendPanelVisible"
      @close="legendPanelVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, provide } from 'vue';
import { MapManager } from '../map';
import { LayerManager } from '../layers';
import { GeoServerService } from '../GeoServerService';
import { UIManager } from '../UIManager';
import type { GeoLayer, GroupLayer, LayerType } from '../types';
import { getGeoServerCapabilitiesUrl } from '../config';
import FloatingMenu from './FloatingMenu.vue';
import SearchPanel from './SearchPanel.vue';
import LayersPanel from './LayersPanel.vue';
import LegendPanel from './LegendPanel.vue';

// Reactive state for panel visibility
const searchPanelVisible = ref(false);
const layersPanelVisible = ref(false);
const legendPanelVisible = ref(false);

// Map and service instances
const mapElement = ref<HTMLElement>();
let mapManager: MapManager | null = null;
let layerManager: LayerManager | null = null;
let geoServerService: GeoServerService | null = null;
let uiManager: UIManager | null = null;

// Reactive layers data
const layers = ref<GeoLayer[]>([]);
const rootGroups = ref<GroupLayer[]>([]);
const ungroupedLayers = ref<GeoLayer[]>([]);
const updateCounter = ref(0); // Force reactivity updates

// Panel toggle functions
const toggleSearchPanel = () => {
  searchPanelVisible.value = !searchPanelVisible.value;
};

const toggleLayersPanel = () => {
  layersPanelVisible.value = !layersPanelVisible.value;
};

const toggleLegendPanel = () => {
  legendPanelVisible.value = !legendPanelVisible.value;
  if (legendPanelVisible.value && layerManager) {
    layerManager.updateLegend();
  }
};

const zoomToHome = () => {
  if (mapManager) {
    mapManager.zoomToHome();
  }
};

// Provide services to child components
provide('mapManager', () => mapManager);
provide('layerManager', () => layerManager);
provide('geoServerService', () => geoServerService);
provide('layers', layers);
provide('rootGroups', rootGroups);
provide('ungroupedLayers', ungroupedLayers);
provide('updateCounter', updateCounter);

onMounted(async () => {
  if (mapElement.value) {
    // Initialize services
    mapManager = new MapManager('map');
    geoServerService = new GeoServerService();

    // Don't use the old UIManager - we'll handle everything in Vue
    // Create a simple layer manager without DOM manipulation
    class VueLayerManager {
      private layers: GeoLayer[] = [];
      private groups: GroupLayer[] = [];
      private rootGroups: GroupLayer[] = [];

      constructor(private mapManager: MapManager, private geoServerService: GeoServerService) {
        this.fetchLayers();
      }

      private async fetchLayers() {
        try {
          console.log('Starting to fetch layers from GeoServer...');
          console.log('GeoServer URL:', getGeoServerCapabilitiesUrl());

          const fetchedLayers = await this.geoServerService.fetchLayers();
          console.log('Fetched layers count:', fetchedLayers.length);
          console.log('Fetched layers:', fetchedLayers);

          if (fetchedLayers.length > 0) {
            this.organizeLayersIntoGroups(fetchedLayers);
            fetchedLayers.forEach(layer => {
              this.layers.push(layer);
              this.mapManager.addLayer(layer);
            });

            // Update reactive data
            layers.value = [...this.layers];
            rootGroups.value = [...this.rootGroups];
            ungroupedLayers.value = this.layers.filter(layer => !layer.groupPath);

            console.log('Updated reactive layers:', layers.value);
            console.log('Updated root groups:', rootGroups.value);
            console.log('Updated ungrouped layers:', ungroupedLayers.value);
          } else {
            console.warn('No layers found in GeoServer');
            // Add some test layers for debugging with groups
            const testLayers: GeoLayer[] = [
              {
                id: 'test_layer_1',
                name: 'Rivers',
                originalName: 'Hydrology/Rivers',
                groupPath: 'Hydrology',
                type: LayerType.WMS,
                visible: false,
                opacity: 0.6,
                zIndex: 10,
                legend_url: '',
                abstract: 'Rivers in the study area'
              },
              {
                id: 'test_layer_2',
                name: 'Lakes',
                originalName: 'Hydrology/Lakes',
                groupPath: 'Hydrology',
                type: LayerType.WMS,
                visible: false,
                opacity: 0.6,
                zIndex: 11,
                legend_url: '',
                abstract: 'Lakes in the study area'
              },
              {
                id: 'test_layer_3',
                name: 'Roads',
                originalName: 'Infrastructure/Roads',
                groupPath: 'Infrastructure',
                type: LayerType.WMS,
                visible: false,
                opacity: 0.6,
                zIndex: 12,
                legend_url: '',
                abstract: 'Road network'
              },
              {
                id: 'test_layer_4',
                name: 'Buildings',
                originalName: 'Infrastructure/Buildings',
                groupPath: 'Infrastructure',
                type: LayerType.WMS,
                visible: false,
                opacity: 0.6,
                zIndex: 13,
                legend_url: '',
                abstract: 'Building footprints'
              },
              {
                id: 'test_layer_5',
                name: 'Elevation',
                originalName: 'Elevation',
                type: LayerType.WMS,
                visible: false,
                opacity: 0.6,
                zIndex: 14,
                legend_url: '',
                abstract: 'Digital elevation model'
              }
            ];

            this.organizeLayersIntoGroups(testLayers);
            testLayers.forEach(layer => {
              this.layers.push(layer);
            });

            // Update reactive data
            layers.value = [...this.layers];
            rootGroups.value = [...this.rootGroups];
            ungroupedLayers.value = this.layers.filter(layer => !layer.groupPath);

            console.log('Added test layers:', layers.value);
            console.log('Test root groups:', rootGroups.value);
            console.log('Test ungrouped layers:', ungroupedLayers.value);
          }
        } catch (error) {
          console.error('Error fetching layers:', error);

          // Add test layers on error for debugging
          const testLayers: GeoLayer[] = [
            {
              id: 'error_test_layer',
              name: 'Error Test Layer',
              originalName: 'Error/Error Test Layer',
              groupPath: 'Error',
              type: LayerType.WMS,
              visible: false,
              opacity: 0.6,
              zIndex: 10,
              legend_url: '',
              abstract: 'This layer was added due to an error'
            }
          ];

          this.organizeLayersIntoGroups(testLayers);
          testLayers.forEach(layer => {
            this.layers.push(layer);
          });

          // Update reactive data
          layers.value = [...this.layers];
          rootGroups.value = [...this.rootGroups];
          ungroupedLayers.value = this.layers.filter(layer => !layer.groupPath);

          console.log('Added error test layers:', layers.value);
        }
      }

      private organizeLayersIntoGroups(fetchedLayers: GeoLayer[]): void {
        this.groups = [];
        this.rootGroups = [];
        const groupMap = new Map<string, GroupLayer>();

        fetchedLayers.forEach(layer => {
          if (!layer.groupPath) return;

          const groupNames = layer.groupPath.split('/');
          let currentPath = '';
          let parentGroup: GroupLayer | undefined;
          let level = 0;

          groupNames.forEach((groupName: string) => {
            currentPath = currentPath ? `${currentPath}/${groupName}` : groupName;
            let group = groupMap.get(currentPath);

            if (!group) {
              group = {
                id: `group_${currentPath.replace(/\//g, '_')}`,
                name: groupName,
                children: [] as (GeoLayer | GroupLayer)[],
                collapsed: false, // Start expanded for better UX
                visible: false,
                level,
                parent: parentGroup,
              } as GroupLayer;
              this.groups.push(group);
              groupMap.set(currentPath, group);
              if (parentGroup) {
                parentGroup.children.push(group);
              } else {
                this.rootGroups.push(group);
              }
            }
            parentGroup = group;
            level++;
          });

          layer.parent = parentGroup;
          if (parentGroup) {
            parentGroup.children.push(layer);
          }
        });
      }

      public toggleLayerVisibility(layerId: string, visible?: boolean): void {
        const layer = this.layers.find(l => l.id === layerId);
        if (!layer) return;

        const newVisibility = visible !== undefined ? visible : !layer.visible;
        console.log(`Toggling layer ${layer.name} visibility from ${layer.visible} to ${newVisibility}`);

        layer.visible = newVisibility;
        this.mapManager.toggleLayerVisibility(layerId, newVisibility);

        // Update parent group states based on children visibility
        this.updateParentGroupStates(layer);

        // Force reactivity update for all components
        updateCounter.value++;

        // Update reactive layers
        layers.value = [...this.layers];
        rootGroups.value = [...this.rootGroups];

        console.log(`Layer ${layer.name} visibility updated. UpdateCounter: ${updateCounter.value}`);
      }

      public updateLayerOpacity(layerId: string, opacity: number): void {
        const layer = this.layers.find(l => l.id === layerId);
        if (!layer) return;

        layer.opacity = opacity;
        this.mapManager.updateLayerOpacity(layerId, opacity);

        // Update reactive layers
        layers.value = [...this.layers];
      }

      public getLayers(): GeoLayer[] {
        return this.layers;
      }

      public getRootGroups(): GroupLayer[] {
        return this.rootGroups;
      }

      public getUngroupedLayers(): GeoLayer[] {
        return this.layers.filter(layer => !layer.groupPath);
      }

      public toggleGroupCollapse(groupId: string): void {
        console.log('Toggling group collapse for:', groupId);
        const group = this.groups.find(g => g.id === groupId);
        if (group) {
          console.log('Group found, current collapsed state:', group.collapsed);
          group.collapsed = !group.collapsed;
          console.log('New collapsed state:', group.collapsed);

          // Force reactivity update
          updateCounter.value++;
          console.log('Incremented update counter to:', updateCounter.value);
        } else {
          console.log('Group not found:', groupId);
        }
      }

      public toggleGroupVisibility(groupId: string): void {
        const group = this.groups.find(g => g.id === groupId);
        if (!group) return;

        const newVisibility = !group.visible;
        console.log(`Toggling group ${group.name} visibility from ${group.visible} to ${newVisibility}`);

        group.visible = newVisibility;
        this.updateChildrenVisibility(group, newVisibility);

        // Force reactivity update for all components
        updateCounter.value++;

        // Update reactive data
        layers.value = [...this.layers];
        rootGroups.value = [...this.rootGroups];

        console.log(`Group ${group.name} visibility updated. UpdateCounter: ${updateCounter.value}`);
      }

      private updateChildrenVisibility(group: GroupLayer, visible: boolean): void {
        group.children.forEach((child: GeoLayer | GroupLayer) => {
          if ('children' in child) {
            const childGroup = child as GroupLayer;
            childGroup.visible = visible;
            this.updateChildrenVisibility(childGroup, visible);
          } else {
            const layer = child as GeoLayer;
            // Update layer visibility directly without triggering parent updates to avoid recursion
            layer.visible = visible;
            this.mapManager.toggleLayerVisibility(layer.id, visible);
          }
        });
      }

      private updateParentGroupStates(layer: GeoLayer): void {
        if (!layer.parent) return;

        const parent = layer.parent;
        const childLayers = parent.children.filter(child => !('children' in child)) as GeoLayer[];
        const childGroups = parent.children.filter(child => 'children' in child) as GroupLayer[];

        // Check if all children (layers and groups) are visible
        const allChildLayersVisible = childLayers.every(child => child.visible);
        const allChildGroupsVisible = childGroups.every(child => child.visible);
        const allChildrenVisible = allChildLayersVisible && allChildGroupsVisible;

        // Check if any children (layers and groups) are visible
        const anyChildLayersVisible = childLayers.some(child => child.visible);
        const anyChildGroupsVisible = childGroups.some(child => child.visible);
        const anyChildrenVisible = anyChildLayersVisible || anyChildGroupsVisible;

        const oldParentVisibility = parent.visible;

        // Update parent visibility based on children
        if (allChildrenVisible) {
          parent.visible = true;
        } else if (!anyChildrenVisible) {
          parent.visible = false;
        }
        // For partial visibility, we keep the current state or could implement indeterminate logic

        if (oldParentVisibility !== parent.visible) {
          console.log(`Updated parent group ${parent.name} visibility from ${oldParentVisibility} to ${parent.visible}`);
        }

        // Recursively update grandparent states
        this.updateParentGroupStates(parent as any);
      }
    }

    layerManager = new VueLayerManager(mapManager, geoServerService) as any;

    // Make mapManager globally available for debugging
    (window as any).mapManager = mapManager;
    (window as any).layerManager = layerManager;
  }
});

onUnmounted(() => {
  // Cleanup services
  if (mapManager) {
    mapManager.destroy();
  }
});
</script>

<style scoped>
#map-container {
  flex: 1;
  position: relative;
  height: 100%;
  min-height: 400px;
}

#map {
  width: 100%;
  height: 100%;
}

@media (max-width: 768px) {
  #map-container {
    min-height: 300px;
  }
}
</style>
